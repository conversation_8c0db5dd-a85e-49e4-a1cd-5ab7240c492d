#!/bin/bash

# New-API 代码更新脚本
# 功能：拉取最新代码、应用补丁、保证功能正常运行
# 版本：v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
NEW_API_DIR="/root/workspace/new-api"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="/root/workspace/backup/$(date +%Y%m%d_%H%M%S)"

# 检查Git仓库状态
check_git_status() {
    log_info "检查Git仓库状态..."
    
    cd "$NEW_API_DIR"
    
    if [ ! -d ".git" ]; then
        log_error "当前目录不是Git仓库"
        return 1
    fi
    
    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "检测到未提交的更改:"
        git status --short
        
        read -p "是否暂存这些更改？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            git stash push -m "Auto-update stash $(date)"
            log_success "更改已暂存"
        else
            log_warning "继续更新，但可能会有冲突"
        fi
    fi
    
    # 显示当前分支和版本
    local current_branch=$(git branch --show-current)
    local current_commit=$(git rev-parse --short HEAD)
    log_info "当前分支: $current_branch"
    log_info "当前提交: $current_commit"
    
    return 0
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."
    
    cd "$NEW_API_DIR"
    
    # 获取远程更新
    if git fetch origin; then
        log_success "远程代码获取成功"
    else
        log_error "远程代码获取失败"
        return 1
    fi
    
    # 检查是否有新的更新
    local updates=$(git log --oneline HEAD..origin/main 2>/dev/null | wc -l)
    if [ "$updates" -eq 0 ]; then
        log_info "没有新的更新"
        return 0
    fi
    
    log_info "发现 $updates 个新提交:"
    git log --oneline HEAD..origin/main | head -10
    
    # 确认是否继续更新
    if [ "${1:-}" != "--force" ]; then
        read -p "是否继续更新？(y/N): " confirm
        if [[ $confirm != [yY] ]]; then
            log_info "更新已取消"
            return 0
        fi
    fi
    
    # 合并更新
    log_info "合并最新代码..."
    if git merge origin/main; then
        log_success "代码合并成功"
        
        # 显示更新后的版本信息
        local new_commit=$(git rev-parse --short HEAD)
        log_info "更新后提交: $new_commit"
        
        return 0
    else
        log_error "代码合并失败，可能存在冲突"
        log_info "请手动解决冲突后重新运行脚本"
        return 1
    fi
}

# 应用子路径补丁
apply_subpath_patches() {
    log_info "应用子路径补丁..."
    
    cd "$NEW_API_DIR"
    
    local patches_applied=0
    
    # 1. 修复 API 基础 URL
    log_info "1. 修复 API 基础 URL..."
    if [ -f "web/src/helpers/api.js" ]; then
        # 备份原文件
        cp web/src/helpers/api.js web/src/helpers/api.js.backup
        
        # 应用修复
        if grep -q "getApiBaseURL" web/src/helpers/api.js; then
            sed -i '/function getApiBaseURL/,/^}/c\
// 动态获取API基础URL\
function getApiBaseURL() {\
  if (import.meta.env.VITE_REACT_APP_SERVER_URL) {\
    return import.meta.env.VITE_REACT_APP_SERVER_URL;\
  }\
\
  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下\
  return '\''/ai'\'';\
}' web/src/helpers/api.js
            
            if grep -q "return '/ai'" web/src/helpers/api.js; then
                log_success "✓ API 基础 URL 修复成功"
                ((patches_applied++))
            else
                log_error "✗ API 基础 URL 修复失败"
            fi
        else
            log_warning "未找到 getApiBaseURL 函数"
        fi
    else
        log_warning "api.js 文件不存在"
    fi
    
    # 2. 修复 Logo 路径
    log_info "2. 修复 Logo 路径..."
    if [ -f "web/src/helpers/utils.js" ]; then
        cp web/src/helpers/utils.js web/src/helpers/utils.js.backup
        
        if sed -i "s|return '/logo.png'|return '/ai/logo.png'|g" web/src/helpers/utils.js; then
            if grep -q "/ai/logo.png" web/src/helpers/utils.js; then
                log_success "✓ Logo 路径修复成功"
                ((patches_applied++))
            else
                log_warning "Logo 路径可能已经正确或修复失败"
            fi
        fi
    else
        log_warning "utils.js 文件不存在"
    fi
    
    # 3. 修复 HTML 模板
    log_info "3. 修复 HTML 模板..."
    if [ -f "web/index.html" ]; then
        cp web/index.html web/index.html.backup
        
        if sed -i 's|href="/logo.png"|href="/ai/logo.png"|g' web/index.html; then
            if grep -q "/ai/logo.png" web/index.html; then
                log_success "✓ HTML 模板修复成功"
                ((patches_applied++))
            else
                log_warning "HTML 模板可能已经正确或修复失败"
            fi
        fi
    else
        log_warning "index.html 文件不存在"
    fi
    
    # 4. 修复 Vite 配置
    log_info "4. 修复 Vite 配置..."
    if [ -f "web/vite.config.js" ]; then
        cp web/vite.config.js web/vite.config.js.backup
        
        if ! grep -q "base: '/ai/'" web/vite.config.js; then
            # 尝试替换现有的 base 配置
            if grep -q "base:" web/vite.config.js; then
                sed -i "s|base: ['\"][^'\"]*['\"]|base: '/ai/'|g" web/vite.config.js
            else
                # 如果没有 base 配置，添加它
                sed -i '/export default defineConfig/a\  base: '\''/ai/'\'',' web/vite.config.js
            fi
            
            if grep -q "base: '/ai/'" web/vite.config.js; then
                log_success "✓ Vite 配置修复成功"
                ((patches_applied++))
            else
                log_error "✗ Vite 配置修复失败"
            fi
        else
            log_success "✓ Vite 配置已正确"
            ((patches_applied++))
        fi
    else
        log_warning "vite.config.js 文件不存在"
    fi
    
    # 5. 修复 React Router 配置
    log_info "5. 修复 React Router 配置..."
    if [ -f "web/src/index.js" ]; then
        cp web/src/index.js web/src/index.js.backup
        
        if ! grep -q 'basename="/ai"' web/src/index.js; then
            # 添加或修复 basename
            if grep -q "BrowserRouter" web/src/index.js; then
                sed -i 's|<BrowserRouter[^>]*>|<BrowserRouter basename="/ai">|g' web/src/index.js
            fi
            
            if grep -q 'basename="/ai"' web/src/index.js; then
                log_success "✓ React Router 配置修复成功"
                ((patches_applied++))
            else
                log_error "✗ React Router 配置修复失败"
            fi
        else
            log_success "✓ React Router 配置已正确"
            ((patches_applied++))
        fi
    else
        log_warning "index.js 文件不存在"
    fi
    
    log_info "补丁应用完成，成功应用 $patches_applied 个补丁"
    return 0
}

# 应用API端点补丁
apply_api_patches() {
    log_info "应用API端点补丁..."

    cd "$NEW_API_DIR"

    local patches_applied=0

    # 1. 修复 Gemini API 端点
    log_info "1. 修复 Gemini API 端点..."
    if [ -f "controller/channel.go" ]; then
        # 备份原文件
        cp controller/channel.go controller/channel.go.backup

        # 检查是否需要修复
        if grep -q 'v1beta/openai/models' controller/channel.go; then
            # 应用修复：将错误的端点替换为正确的端点
            sed -i 's|v1beta/openai/models|v1beta/models|g' controller/channel.go

            if grep -q 'v1beta/models' controller/channel.go && ! grep -q 'v1beta/openai/models' controller/channel.go; then
                log_success "✓ Gemini API 端点修复成功"
                ((patches_applied++))
            else
                log_error "✗ Gemini API 端点修复失败"
            fi
        else
            log_success "✓ Gemini API 端点已正确"
            ((patches_applied++))
        fi
    else
        log_warning "controller/channel.go 文件不存在"
    fi

    log_info "API端点补丁应用完成，成功应用 $patches_applied 个补丁"
    return 0
}

# 验证配置
verify_patches() {
    log_info "验证补丁应用结果..."
    
    cd "$NEW_API_DIR"
    
    local verification_passed=0
    local total_checks=6
    
    # 检查 API 基础 URL
    if grep -q "return '/ai'" web/src/helpers/api.js 2>/dev/null; then
        log_success "✓ API 基础 URL 配置正确"
        ((verification_passed++))
    else
        log_error "✗ API 基础 URL 配置错误"
    fi
    
    # 检查 Logo 路径
    if grep -q "/ai/logo.png" web/src/helpers/utils.js 2>/dev/null; then
        log_success "✓ Logo 路径配置正确"
        ((verification_passed++))
    else
        log_error "✗ Logo 路径配置错误"
    fi
    
    # 检查 HTML 模板
    if grep -q "/ai/logo.png" web/index.html 2>/dev/null; then
        log_success "✓ HTML 模板配置正确"
        ((verification_passed++))
    else
        log_error "✗ HTML 模板配置错误"
    fi
    
    # 检查 Vite 配置
    if grep -q "base: '/ai/'" web/vite.config.js 2>/dev/null; then
        log_success "✓ Vite 配置正确"
        ((verification_passed++))
    else
        log_error "✗ Vite 配置错误"
    fi
    
    # 检查 React Router 配置
    if grep -q 'basename="/ai"' web/src/index.js 2>/dev/null; then
        log_success "✓ React Router 配置正确"
        ((verification_passed++))
    else
        log_error "✗ React Router 配置错误"
    fi

    # 检查 Gemini API 端点
    if grep -q 'v1beta/models' controller/channel.go 2>/dev/null && ! grep -q 'v1beta/openai/models' controller/channel.go 2>/dev/null; then
        log_success "✓ Gemini API 端点配置正确"
        ((verification_passed++))
    else
        log_error "✗ Gemini API 端点配置错误"
    fi
    
    log_info "配置验证完成: $verification_passed/$total_checks 项通过"
    
    if [ $verification_passed -eq $total_checks ]; then
        log_success "所有配置验证通过"
        return 0
    else
        log_warning "部分配置验证失败，可能需要手动检查"
        return 1
    fi
}

# 创建备份
create_backup() {
    log_info "创建更新前备份..."
    
    mkdir -p "$BACKUP_DIR"
    
    cd "$NEW_API_DIR"
    
    # 备份关键文件
    mkdir -p "$BACKUP_DIR/source_files"
    cp web/src/helpers/api.js "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "api.js 备份失败"
    cp web/src/helpers/utils.js "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "utils.js 备份失败"
    cp web/index.html "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "index.html 备份失败"
    cp web/vite.config.js "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "vite.config.js 备份失败"
    cp web/src/index.js "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "index.js 备份失败"
    cp controller/channel.go "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "channel.go 备份失败"
    
    # 记录当前状态
    git log --oneline -10 > "$BACKUP_DIR/git_history.txt" 2>/dev/null || log_warning "Git 历史记录失败"
    
    log_success "备份创建完成: $BACKUP_DIR"
}

# 显示帮助信息
show_help() {
    echo "New-API 代码更新脚本"
    echo
    echo "用法: $0 <命令> [选项]"
    echo
    echo "命令:"
    echo "  check                   - 检查Git仓库状态"
    echo "  pull [--force]          - 拉取最新代码"
    echo "  patch                   - 应用子路径补丁"
    echo "  api-patch               - 应用API端点补丁"
    echo "  verify                  - 验证补丁配置"
    echo "  update [--force]        - 完整更新流程（推荐）"
    echo "  help                    - 显示帮助信息"
    echo
    echo "选项:"
    echo "  --force                 - 强制执行，不询问确认"
    echo
    echo "示例:"
    echo "  $0 update                       # 完整更新流程"
    echo "  $0 pull --force                 # 强制拉取最新代码"
    echo "  $0 patch                        # 仅应用子路径补丁"
    echo "  $0 api-patch                    # 仅应用API端点补丁"
}

# 完整更新流程
full_update() {
    local force_flag="$1"
    
    log_info "开始完整更新流程..."
    
    # 创建备份
    create_backup
    
    # 检查Git状态
    if ! check_git_status; then
        log_error "Git状态检查失败"
        return 1
    fi
    
    # 拉取最新代码
    if ! pull_latest_code "$force_flag"; then
        log_error "代码拉取失败"
        return 1
    fi
    
    # 应用子路径补丁
    if ! apply_subpath_patches; then
        log_error "子路径补丁应用失败"
        return 1
    fi

    # 应用API端点补丁
    if ! apply_api_patches; then
        log_error "API端点补丁应用失败"
        return 1
    fi
    
    # 验证配置
    if ! verify_patches; then
        log_warning "配置验证失败，但继续执行"
    fi
    
    log_success "完整更新流程完成"
    log_info "备份目录: $BACKUP_DIR"
    log_info "建议运行服务重启: $SCRIPT_DIR/service.sh rebuild"
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_git_status
            ;;
        "pull")
            check_git_status && pull_latest_code "$2"
            ;;
        "patch")
            apply_subpath_patches
            ;;
        "api-patch")
            apply_api_patches
            ;;
        "verify")
            verify_patches
            ;;
        "update")
            full_update "$2"
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
