#!/bin/bash

# Gemini API 端点修复补丁脚本
# 功能：修复 Gemini API 模型列表端点错误
# 版本：v1.0
# 问题：官方代码使用了错误的端点 v1beta/openai/models，应该是 v1beta/models

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
NEW_API_DIR="/root/workspace/new-api"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="/root/workspace/backup/gemini-patch-$(date +%Y%m%d_%H%M%S)"

# 检查文件是否存在
check_files() {
    log_info "检查目标文件..."
    
    if [ ! -f "$NEW_API_DIR/controller/channel.go" ]; then
        log_error "目标文件不存在: $NEW_API_DIR/controller/channel.go"
        return 1
    fi
    
    log_success "目标文件检查通过"
    return 0
}

# 检查是否需要修复
check_need_patch() {
    log_info "检查是否需要应用补丁..."
    
    cd "$NEW_API_DIR"
    
    if grep -q 'v1beta/openai/models' controller/channel.go; then
        log_warning "发现错误的 Gemini API 端点: v1beta/openai/models"
        log_info "需要修复为正确的端点: v1beta/models"
        return 0
    else
        log_success "Gemini API 端点已正确，无需修复"
        return 1
    fi
}

# 创建备份
create_backup() {
    log_info "创建补丁前备份..."
    
    mkdir -p "$BACKUP_DIR"
    
    cd "$NEW_API_DIR"
    
    # 备份目标文件
    cp controller/channel.go "$BACKUP_DIR/channel.go.backup"
    
    # 记录当前状态
    echo "=== Gemini API 端点修复补丁备份 ===" > "$BACKUP_DIR/patch_info.txt"
    echo "备份时间: $(date)" >> "$BACKUP_DIR/patch_info.txt"
    echo "备份原因: 修复 Gemini API 端点错误" >> "$BACKUP_DIR/patch_info.txt"
    echo "错误端点: v1beta/openai/models" >> "$BACKUP_DIR/patch_info.txt"
    echo "正确端点: v1beta/models" >> "$BACKUP_DIR/patch_info.txt"
    echo "" >> "$BACKUP_DIR/patch_info.txt"
    
    # 记录修改前的内容
    echo "=== 修改前的相关代码 ===" >> "$BACKUP_DIR/patch_info.txt"
    grep -n "v1beta.*models" controller/channel.go >> "$BACKUP_DIR/patch_info.txt" || true
    
    log_success "备份创建完成: $BACKUP_DIR"
}

# 应用补丁
apply_patch() {
    log_info "应用 Gemini API 端点修复补丁..."
    
    cd "$NEW_API_DIR"
    
    # 应用修复：将错误的端点替换为正确的端点
    if sed -i 's|v1beta/openai/models|v1beta/models|g' controller/channel.go; then
        log_success "补丁应用成功"
    else
        log_error "补丁应用失败"
        return 1
    fi
    
    return 0
}

# 验证修复结果
verify_patch() {
    log_info "验证补丁修复结果..."
    
    cd "$NEW_API_DIR"
    
    # 检查是否还存在错误的端点
    if grep -q 'v1beta/openai/models' controller/channel.go; then
        log_error "验证失败：仍然存在错误的端点"
        return 1
    fi
    
    # 检查是否存在正确的端点
    if grep -q 'v1beta/models' controller/channel.go; then
        log_success "验证成功：端点已修复为正确格式"
        
        # 显示修复后的相关代码
        log_info "修复后的相关代码:"
        grep -n "v1beta/models" controller/channel.go | head -5
        
        return 0
    else
        log_error "验证失败：未找到正确的端点"
        return 1
    fi
}

# 显示修复详情
show_patch_details() {
    echo
    log_info "=== Gemini API 端点修复详情 ==="
    echo
    echo "问题描述："
    echo "  官方代码中使用了错误的 Gemini API 端点"
    echo "  错误端点: https://generativelanguage.googleapis.com/v1beta/openai/models"
    echo "  正确端点: https://generativelanguage.googleapis.com/v1beta/models"
    echo
    echo "影响功能："
    echo "  - 渠道管理页面的「获取模型」功能"
    echo "  - Gemini 渠道的模型列表获取"
    echo
    echo "修复方法："
    echo "  将 controller/channel.go 中的 'v1beta/openai/models' 替换为 'v1beta/models'"
    echo
    echo "参考文档："
    echo "  Google Gemini API 官方文档: https://ai.google.dev/api/models"
    echo
}

# 显示帮助信息
show_help() {
    echo "Gemini API 端点修复补丁脚本"
    echo
    echo "用法: $0 <命令>"
    echo
    echo "命令:"
    echo "  check                   - 检查是否需要修复"
    echo "  apply                   - 应用补丁修复"
    echo "  verify                  - 验证修复结果"
    echo "  details                 - 显示修复详情"
    echo "  help                    - 显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 check                        # 检查是否需要修复"
    echo "  $0 apply                        # 应用修复补丁"
    echo "  $0 verify                       # 验证修复结果"
}

# 完整修复流程
full_patch() {
    log_info "开始 Gemini API 端点完整修复流程..."
    
    # 检查文件
    if ! check_files; then
        log_error "文件检查失败"
        return 1
    fi
    
    # 检查是否需要修复
    if ! check_need_patch; then
        log_info "无需修复，退出"
        return 0
    fi
    
    # 创建备份
    create_backup
    
    # 应用补丁
    if ! apply_patch; then
        log_error "补丁应用失败"
        return 1
    fi
    
    # 验证结果
    if ! verify_patch; then
        log_error "补丁验证失败"
        return 1
    fi
    
    log_success "Gemini API 端点修复完成"
    log_info "备份目录: $BACKUP_DIR"
    log_info "建议重新编译并重启服务以使修复生效"
    
    return 0
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_files && check_need_patch
            ;;
        "apply")
            full_patch
            ;;
        "verify")
            verify_patch
            ;;
        "details")
            show_patch_details
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
