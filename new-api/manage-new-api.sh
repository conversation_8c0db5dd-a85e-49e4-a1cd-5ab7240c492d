#!/bin/bash

# New-API 综合管理脚本
# 功能：统一管理 New-API 的所有操作
# 版本：v2.0
# 作者：AI Assistant

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${CYAN}${BOLD}$1${NC}"; }

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SH_DIR="$SCRIPT_DIR/sh"
NEW_API_DIR="/root/workspace/new-api"

# 检查脚本目录
check_scripts() {
    if [ ! -d "$SH_DIR" ]; then
        log_error "脚本目录不存在: $SH_DIR"
        return 1
    fi
    
    local required_scripts=("service.sh" "database.sh" "update.sh" "monitor.sh" "quick-fix.sh" "auto-update.sh" "gemini-patch.sh")
    local missing_scripts=()
    
    for script in "${required_scripts[@]}"; do
        if [ ! -f "$SH_DIR/$script" ]; then
            missing_scripts+=("$script")
        fi
    done
    
    if [ ${#missing_scripts[@]} -gt 0 ]; then
        log_error "缺少脚本文件: ${missing_scripts[*]}"
        return 1
    fi
    
    # 确保脚本可执行
    chmod +x "$SH_DIR"/*.sh
    
    return 0
}

# 显示主菜单
show_main_menu() {
    clear
    log_header "========================================"
    log_header "        New-API 综合管理系统"
    log_header "========================================"
    echo
    echo -e "${CYAN}服务管理:${NC}"
    echo "  1) 启动服务          2) 停止服务"
    echo "  3) 重启服务          4) 重新构建"
    echo "  5) 服务状态          6) 查看日志"
    echo
    echo -e "${CYAN}代码管理:${NC}"
    echo "  7) 拉取最新代码      8) 应用补丁"
    echo "  9) 完整更新          10) 快速修复"
    echo
    echo -e "${CYAN}数据库管理:${NC}"
    echo "  11) 备份数据库       12) 恢复数据库"
    echo "  13) 数据库维护       14) 清理备份"
    echo
    echo -e "${CYAN}监控维护:${NC}"
    echo "  15) 系统监控         16) 快速检查"
    echo "  17) 进入容器         18) 自动更新"
    echo
    echo -e "${CYAN}其他操作:${NC}"
    echo "  19) 显示帮助         0) 退出"
    echo
    echo "========================================"
}

# 服务管理菜单
service_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "           服务管理菜单"
        log_header "========================================"
        echo
        echo "1) 启动服务"
        echo "2) 停止服务"
        echo "3) 重启服务"
        echo "4) 重新构建"
        echo "5) 服务状态"
        echo "6) 查看日志"
        echo "7) 跟踪日志"
        echo "8) 进入容器"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-8]: " choice
        
        case $choice in
            1) "$SH_DIR/service.sh" start; pause ;;
            2) "$SH_DIR/service.sh" stop; pause ;;
            3) "$SH_DIR/service.sh" restart; pause ;;
            4) "$SH_DIR/service.sh" rebuild; pause ;;
            5) "$SH_DIR/service.sh" status; pause ;;
            6) 
                echo "选择服务: 1) new-api  2) nginx  3) all"
                read -p "请选择 [1-3]: " svc_choice
                case $svc_choice in
                    1) "$SH_DIR/service.sh" logs new-api ;;
                    2) "$SH_DIR/service.sh" logs nginx ;;
                    3) "$SH_DIR/service.sh" logs all ;;
                    *) log_error "无效选择" ;;
                esac
                pause
                ;;
            7)
                echo "选择服务: 1) new-api  2) nginx"
                read -p "请选择 [1-2]: " svc_choice
                case $svc_choice in
                    1) "$SH_DIR/service.sh" follow new-api ;;
                    2) "$SH_DIR/service.sh" follow nginx ;;
                    *) log_error "无效选择" ;;
                esac
                ;;
            8)
                echo "选择容器: 1) new-api  2) nginx  3) mysql"
                read -p "请选择 [1-3]: " svc_choice
                case $svc_choice in
                    1) "$SH_DIR/service.sh" enter new-api ;;
                    2) "$SH_DIR/service.sh" enter nginx ;;
                    3) "$SH_DIR/service.sh" enter mysql ;;
                    *) log_error "无效选择" ;;
                esac
                ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 数据库管理菜单
database_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "          数据库管理菜单"
        log_header "========================================"
        echo
        echo "1) 检查数据库连接"
        echo "2) 备份数据库"
        echo "3) 恢复数据库"
        echo "4) 数据库维护"
        echo "5) 显示备份列表"
        echo "6) 清理旧备份"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-6]: " choice
        
        case $choice in
            1) "$SH_DIR/database.sh" check; pause ;;
            2) 
                read -p "输入备份名称（可选，回车使用时间戳）: " backup_name
                "$SH_DIR/database.sh" backup "$backup_name"
                pause
                ;;
            3)
                "$SH_DIR/database.sh" list
                echo
                read -p "输入备份文件完整路径: " backup_file
                if [ -n "$backup_file" ]; then
                    "$SH_DIR/database.sh" restore "$backup_file"
                fi
                pause
                ;;
            4) "$SH_DIR/database.sh" maintain; pause ;;
            5) "$SH_DIR/database.sh" list; pause ;;
            6)
                read -p "输入要保留的天数（默认30天）: " days
                "$SH_DIR/database.sh" cleanup "${days:-30}"
                pause
                ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 代码管理菜单
code_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "           代码管理菜单"
        log_header "========================================"
        echo
        echo "1) 检查Git状态"
        echo "2) 拉取最新代码"
        echo "3) 应用子路径补丁"
        echo "4) 应用API端点补丁"
        echo "5) Gemini端点修复"
        echo "6) 验证配置"
        echo "7) 完整更新流程"
        echo "8) 快速修复"
        echo "9) 自动更新（完整流程）"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-9]: " choice
        
        case $choice in
            1) "$SH_DIR/update.sh" check; pause ;;
            2) 
                read -p "是否强制拉取？(y/N): " force
                if [[ $force == [yY] ]]; then
                    "$SH_DIR/update.sh" pull --force
                else
                    "$SH_DIR/update.sh" pull
                fi
                pause
                ;;
            3) "$SH_DIR/update.sh" patch; pause ;;
            4) "$SH_DIR/update.sh" api-patch; pause ;;
            5) "$SH_DIR/gemini-patch.sh" apply; pause ;;
            6) "$SH_DIR/update.sh" verify; pause ;;
            7)
                read -p "是否强制更新？(y/N): " force
                if [[ $force == [yY] ]]; then
                    "$SH_DIR/update.sh" update --force
                else
                    "$SH_DIR/update.sh" update
                fi
                pause
                ;;
            8) "$SH_DIR/quick-fix.sh"; pause ;;
            9) "$SH_DIR/auto-update.sh"; pause ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 监控菜单
monitor_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "           监控维护菜单"
        log_header "========================================"
        echo
        echo "1) 完整系统监控"
        echo "2) 快速状态检查"
        echo "3) 服务状态"
        echo "4) 系统资源"
        echo "5) 错误日志分析"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-5]: " choice
        
        case $choice in
            1) "$SH_DIR/monitor.sh" monitor; pause ;;
            2) "$SH_DIR/monitor.sh" quick; pause ;;
            3) "$SH_DIR/service.sh" status; pause ;;
            4)
                echo "=== 系统资源使用情况 ==="
                echo "内存使用:"
                free -h
                echo
                echo "磁盘使用:"
                df -h /root/workspace/
                echo
                echo "CPU使用:"
                top -bn1 | head -5
                pause
                ;;
            5)
                echo "=== 错误日志分析 ==="
                echo "New-API 错误日志:"
                docker-compose -f "$NEW_API_DIR/docker-compose.yml" logs --since=1h new-api 2>/dev/null | grep -i error | tail -10 || echo "无错误日志"
                echo
                echo "Nginx 错误日志:"
                docker-compose -f "/root/workspace/shared/nginx/docker-compose.yml" logs --since=1h nginx-proxy 2>/dev/null | grep -E "(error|404|500)" | tail -10 || echo "无错误日志"
                pause
                ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 暂停函数
pause() {
    echo
    read -p "按回车键继续..."
}

# 快速操作函数
quick_start() {
    log_info "快速启动 New-API..."
    "$SH_DIR/service.sh" start
}

quick_stop() {
    log_info "快速停止 New-API..."
    "$SH_DIR/service.sh" stop
}

quick_restart() {
    log_info "快速重启 New-API..."
    "$SH_DIR/service.sh" restart
}

quick_status() {
    log_info "快速状态检查..."
    "$SH_DIR/monitor.sh" quick
}

quick_backup() {
    log_info "快速数据库备份..."
    "$SH_DIR/database.sh" backup
}

quick_update() {
    log_info "快速更新..."
    "$SH_DIR/update.sh" update --force
    echo
    read -p "是否重新构建服务？(Y/n): " rebuild
    if [[ $rebuild != [nN] ]]; then
        "$SH_DIR/service.sh" rebuild
    fi
}

# 显示帮助信息
show_help() {
    clear
    log_header "========================================"
    log_header "        New-API 管理脚本帮助"
    log_header "========================================"
    echo
    echo -e "${CYAN}基本用法:${NC}"
    echo "  $0                      # 启动交互式菜单"
    echo "  $0 <命令>               # 直接执行命令"
    echo
    echo -e "${CYAN}快速命令:${NC}"
    echo "  start                   # 启动服务"
    echo "  stop                    # 停止服务"
    echo "  restart                 # 重启服务"
    echo "  status                  # 状态检查"
    echo "  backup                  # 数据库备份"
    echo "  update                  # 代码更新"
    echo "  fix                     # 快速修复"
    echo "  monitor                 # 系统监控"
    echo
    echo -e "${CYAN}子脚本说明:${NC}"
    echo "  sh/service.sh           # 服务管理"
    echo "  sh/database.sh          # 数据库管理"
    echo "  sh/update.sh            # 代码更新"
    echo "  sh/monitor.sh           # 系统监控"
    echo "  sh/quick-fix.sh         # 快速修复"
    echo "  sh/auto-update.sh       # 自动更新"
    echo
    echo -e "${CYAN}配置文件:${NC}"
    echo "  new-api错误指南.md      # 错误解决指南"
    echo "  new-api更新指导.md      # 更新操作指导"
    echo
    pause
}

# 主函数
main() {
    # 检查脚本环境
    if ! check_scripts; then
        log_error "脚本环境检查失败"
        exit 1
    fi
    
    # 处理命令行参数
    case "${1:-menu}" in
        "start") quick_start ;;
        "stop") quick_stop ;;
        "restart") quick_restart ;;
        "status") quick_status ;;
        "backup") quick_backup ;;
        "update") quick_update ;;
        "fix") "$SH_DIR/quick-fix.sh" ;;
        "monitor") "$SH_DIR/monitor.sh" monitor ;;
        "help") show_help ;;
        "menu"|"")
            # 交互式菜单
            while true; do
                show_main_menu
                read -p "请选择操作 [0-19]: " choice
                
                case $choice in
                    1) "$SH_DIR/service.sh" start; pause ;;
                    2) "$SH_DIR/service.sh" stop; pause ;;
                    3) "$SH_DIR/service.sh" restart; pause ;;
                    4) "$SH_DIR/service.sh" rebuild; pause ;;
                    5) "$SH_DIR/service.sh" status; pause ;;
                    6) service_menu ;;
                    7) "$SH_DIR/update.sh" pull; pause ;;
                    8) "$SH_DIR/update.sh" patch; pause ;;
                    9) code_menu ;;
                    10) "$SH_DIR/quick-fix.sh"; pause ;;
                    11) "$SH_DIR/database.sh" backup; pause ;;
                    12) database_menu ;;
                    13) "$SH_DIR/database.sh" maintain; pause ;;
                    14) "$SH_DIR/database.sh" cleanup; pause ;;
                    15) "$SH_DIR/monitor.sh" monitor; pause ;;
                    16) "$SH_DIR/monitor.sh" quick; pause ;;
                    17) service_menu ;;
                    18) "$SH_DIR/auto-update.sh"; pause ;;
                    19) show_help ;;
                    0) 
                        log_success "感谢使用 New-API 管理系统！"
                        exit 0
                        ;;
                    *) 
                        log_error "无效选择，请重新输入"
                        sleep 2
                        ;;
                esac
            done
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
